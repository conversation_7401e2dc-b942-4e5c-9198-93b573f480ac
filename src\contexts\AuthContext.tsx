'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { createClient, UserProfile, AuthState } from '@/utils/supabase/client';
import { Session, User } from '@supabase/supabase-js';

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData: { name: string; phone: string; role?: string }) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    initialized: false,
  });

  const supabase = createClient();

  // Função para extrair perfil do usuário
  const extractUserProfile = (user: User): UserProfile => {
    return {
      id: user.id,
      email: user.email || '',
      name: user.user_metadata?.name || user.user_metadata?.full_name || '',
      phone: user.user_metadata?.phone || '',
      role: user.user_metadata?.role || 'Usuario',
      created_at: user.created_at,
      updated_at: user.updated_at || user.created_at,
      pagamento_confirmado: user.user_metadata?.pagamento_confirmado || false,
      onboarding_pendente: user.user_metadata?.onboarding_pendente || false,
      plano_selecionado: user.user_metadata?.plano_selecionado || null,
    };
  };

  // Função para atualizar o estado de autenticação
  const updateAuthState = (session: Session | null) => {
    if (session?.user) {
      const userProfile = extractUserProfile(session.user);
      setAuthState({
        user: userProfile,
        session,
        loading: false,
        initialized: true,
      });
    } else {
      setAuthState({
        user: null,
        session: null,
        loading: false,
        initialized: true,
      });
    }
  };

  // Inicializar autenticação
  useEffect(() => {
    let mounted = true;

    // Obter sessão inicial
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Erro ao obter sessão:', error);
        }
        if (mounted) {
          updateAuthState(session);
        }
      } catch (error) {
        console.error('Erro ao inicializar autenticação:', error);
        if (mounted) {
          setAuthState(prev => ({ ...prev, loading: false, initialized: true }));
        }
      }
    };

    getInitialSession();

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session?.user?.user_metadata);
        if (mounted) {
          updateAuthState(session);
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  // Função de login
  const signIn = async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Função de cadastro
  const signUp = async (
    email: string, 
    password: string, 
    userData: { name: string; phone: string; role?: string }
  ) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: userData.name,
            full_name: userData.name,
            phone: userData.phone,
            role: userData.role || 'Usuario', // Papel padrão "Cliente" (Usuario)
          },
        },
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Função de logout
  const signOut = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  // Função de recuperação de senha
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/callback?next=/reset-password`,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Função para atualizar perfil
  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      const { error } = await supabase.auth.updateUser({
        data: updates,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Função para forçar atualização dos dados do usuário
  const refreshUser = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Erro ao atualizar sessão:', error);
      } else {
        updateAuthState(session);
      }
    } catch (error) {
      console.error('Erro ao forçar atualização do usuário:', error);
    }
  };

  const value: AuthContextType = {
    ...authState,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook para usar o contexto de autenticação
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
}

// Hook para verificar se o usuário tem um papel específico
export function useRole(requiredRole: string | string[]) {
  const { user } = useAuth();
  
  if (!user) return false;
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(user.role);
  }
  
  return user.role === requiredRole;
}

// Hook para verificar se o usuário está autenticado
export function useRequireAuth() {
  const { user, loading, initialized } = useAuth();
  
  return {
    isAuthenticated: !!user,
    isLoading: loading || !initialized,
    user,
  };
}
