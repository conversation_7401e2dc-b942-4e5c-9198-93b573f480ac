'use client';

import React, { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useGerenciamentoAgendamentos } from '@/hooks/useGerenciamentoAgendamentos';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { EstatisticasAgendamentos } from '@/components/agendamentos/EstatisticasAgendamentos';
import { CardAgendamento } from '@/components/agendamentos/CardAgendamento';

import Link from 'next/link';
import { parseISO, startOfWeek, endOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function ProprietarioDashboardPage() {
  return (
    <ProtectedRoute requiredRole="Proprietario">
      <ProprietarioDashboard />
    </ProtectedRoute>
  );
}

function ProprietarioDashboard() {
  const { user } = useAuth();
  const [periodoSelecionado, setPeriodoSelecionado] = useState<'hoje' | 'semana' | 'mes'>('hoje');

  const {
    agendamentos,
    estatisticas,
    loading,
    buscarAgendamentos,
    confirmarAgendamento,
    recusarAgendamento
  } = useGerenciamentoAgendamentos();

  // Carregar agendamentos ao montar o componente
  useEffect(() => {
    // Converter período para filtros de data
    const agora = new Date();
    let filtros = {};

    switch (periodoSelecionado) {
      case 'hoje': {
        filtros = {
          data_inicio: agora.toISOString().split('T')[0],
          data_fim: agora.toISOString().split('T')[0]
        };
        break;
      }
      case 'semana': {
        const inicioSemana = new Date(agora);
        inicioSemana.setDate(agora.getDate() - agora.getDay());
        const fimSemana = new Date(inicioSemana);
        fimSemana.setDate(inicioSemana.getDate() + 6);
        filtros = {
          data_inicio: inicioSemana.toISOString().split('T')[0],
          data_fim: fimSemana.toISOString().split('T')[0]
        };
        break;
      }
      case 'mes': {
        const inicioMes = new Date(agora.getFullYear(), agora.getMonth(), 1);
        const fimMes = new Date(agora.getFullYear(), agora.getMonth() + 1, 0);
        filtros = {
          data_inicio: inicioMes.toISOString().split('T')[0],
          data_fim: fimMes.toISOString().split('T')[0]
        };
        break;
      }
    }

    buscarAgendamentos(filtros);
  }, [buscarAgendamentos, periodoSelecionado]);

  // Filtrar agendamentos por período
  const obterAgendamentosPorPeriodo = () => {
    const agora = new Date();

    switch (periodoSelecionado) {
      case 'hoje': {
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento.toDateString() === agora.toDateString();
        });
      }
      case 'semana': {
        const inicioSemana = startOfWeek(agora, { locale: ptBR });
        const fimSemana = endOfWeek(agora, { locale: ptBR });
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento >= inicioSemana && dataAgendamento <= fimSemana;
        });
      }
      case 'mes': {
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento.getMonth() === agora.getMonth() &&
                 dataAgendamento.getFullYear() === agora.getFullYear();
        });
      }
      default:
        return agendamentos;
    }
  };

  const agendamentosPendentes = agendamentos.filter(a => a.status_agendamento === 'Pendente');

  // Agendamentos próximos ao prazo (2 horas)
  const agendamentosProximoPrazo = agendamentosPendentes.filter(a => {
    const prazo = parseISO(a.prazo_confirmacao);
    const agora = new Date();
    const duasHoras = 2 * 60 * 60 * 1000;
    return (prazo.getTime() - agora.getTime()) < duasHoras && (prazo.getTime() - agora.getTime()) > 0;
  });

  // Função para obter texto do período
  const obterTextoPeriodo = (periodo: string) => {
    switch (periodo) {
      case 'hoje': return 'Hoje';
      case 'semana': return 'Semana';
      case 'mes': return 'Mês';
      default: return 'Hoje';
    }
  };

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Dashboard do Proprietário
              </h1>
              <p className="text-[var(--text-secondary)]">
                Bem-vindo, {user?.name ?? 'Proprietário'}!
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Seletor de período */}
              <div className="flex bg-[var(--surface)] rounded-lg p-1">
                {(['hoje', 'semana', 'mes'] as const).map((periodo) => (
                  <button
                    key={periodo}
                    onClick={() => setPeriodoSelecionado(periodo)}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      periodoSelecionado === periodo
                        ? 'bg-[var(--primary)] text-white'
                        : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
                    }`}
                  >
                    {obterTextoPeriodo(periodo)}
                  </button>
                ))}
              </div>
              <Link href="/">
                <Button variant="outline">
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Alertas de agendamentos próximos ao prazo */}
        {agendamentosProximoPrazo.length > 0 && (
          <Card className="mb-6 border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="text-orange-600">⚠️</div>
                <div>
                  <div className="font-semibold text-orange-800">
                    Atenção: {agendamentosProximoPrazo.length} agendamento(s) próximo(s) ao prazo
                  </div>
                  <div className="text-sm text-orange-700">
                    Você tem agendamentos pendentes que precisam ser confirmados ou recusados em breve.
                  </div>
                </div>
                <div className="ml-auto">
                  <Link href="/proprietario/agendamentos">
                    <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                      Ver Agendamentos
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Estatísticas do Período */}
        {estatisticas && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                Estatísticas - {obterTextoPeriodo(periodoSelecionado)}
              </h2>
            </div>
            <EstatisticasAgendamentos
              estatisticas={estatisticas}
              loading={loading}
              periodo={periodoSelecionado}
            />
          </div>
        )}

        {/* Agendamentos Pendentes */}
        {agendamentosPendentes.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                Solicitações Pendentes ({agendamentosPendentes.length})
              </h2>
              <Link href="/proprietario/agendamentos">
                <Button variant="outline" size="sm">
                  Ver Todos
                </Button>
              </Link>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {agendamentosPendentes.slice(0, 4).map(agendamento => (
                <CardAgendamento
                  key={agendamento.agendamento_id}
                  agendamento={agendamento}
                  onConfirmar={confirmarAgendamento}
                  onRecusar={recusarAgendamento}
                  loading={loading}
                  userRole="Proprietario"
                  mostrarAcoes={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* Agendamentos do Dia */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-[var(--text-primary)]">
              Agendamentos de Hoje
            </h2>
            <Link href="/proprietario/agendamentos">
              <Button variant="outline" size="sm">
                Ver Agenda Completa
              </Button>
            </Link>
          </div>

          {(() => {
            const agendamentosHoje = obterAgendamentosPorPeriodo().filter(a => {
              const hoje = new Date();
              const dataAgendamento = parseISO(a.data_hora_inicio);
              return dataAgendamento.toDateString() === hoje.toDateString();
            });

            if (agendamentosHoje.length === 0) {
              return (
                <Card>
                  <CardContent className="p-8 text-center">
                    <div className="text-[var(--text-secondary)]">
                      <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-lg font-medium">Nenhum agendamento para hoje</p>
                      <p className="text-sm">Aproveite para organizar seu estabelecimento!</p>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            return (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {agendamentosHoje.slice(0, 6).map(agendamento => (
                  <CardAgendamento
                    key={agendamento.agendamento_id}
                    agendamento={agendamento}
                    onConfirmar={confirmarAgendamento}
                    onRecusar={recusarAgendamento}
                    loading={loading}
                    userRole="Proprietario"
                    mostrarAcoes={true}
                  />
                ))}
              </div>
            );
          })()}
        </div>

        {/* Ações Principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Agenda */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Agenda
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Visualize e gerencie agendamentos
              </p>
              <Link href="/proprietario/agendamentos">
                <Button className="w-full">
                  Ver Agenda
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Serviços */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                Serviços
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Configure serviços e preços
              </p>
              <Link href="/proprietario/servicos">
                <Button className="w-full">
                  Gerenciar Serviços
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Combos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                Combos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Crie combos promocionais para aumentar vendas
              </p>
              <Link href="/proprietario/combos">
                <Button className="w-full">
                  Gerenciar Combos
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Colaboradores */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Colaboradores
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Convide e gerencie colaboradores
              </p>
              <Link href="/proprietario/colaboradores">
                <Button className="w-full">
                  Gerenciar Colaboradores
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Horários */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Horários
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Configure horários de funcionamento
              </p>
              <Link href="/proprietario/horarios">
                <Button className="w-full">
                  Gerenciar Horários
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Configurações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Configurações
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Pagamentos online e outras configurações
              </p>
              <Link href="/proprietario/configuracoes">
                <Button className="w-full">
                  Configurar
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Relatórios */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Relatórios
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Visualize relatórios e estatísticas
              </p>
              <Link href="/proprietario/relatorios">
                <Button className="w-full">
                  Ver Relatórios
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
